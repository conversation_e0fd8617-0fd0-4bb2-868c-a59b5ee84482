[2025-07-31 10:51:49] local.INFO: Login attempt {"email":"<EMAIL>"} 
[2025-07-31 10:51:49] local.INFO: User login successful {"user_id":"a271b5c2-328b-4685-a57a-7665711232b8","email":"<EMAIL>","ip_address":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"uYCUfFmdwMeEjoAKn1EgqL7Wb7iAu5bI3z9f1Jb0","request_url":"http://127.0.0.1:8000/login-post","request_method":"POST","host":"127.0.0.1","server_port":8000,"referer":"http://127.0.0.1:8000/login","login_time":"2025-07-31 10:51:49"} 
[2025-07-31 10:51:53] local.INFO: User logged out {"user_uuid":"a271b5c2-328b-4685-a57a-7665711232b8","email":"<EMAIL>","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","logged_out_at":"2025-07-31 10:51:53"} 
[2025-07-31 10:52:29] local.ERROR: Command "session:flush" is not defined.

Did you mean one of these?
    queue:flush
    session:table {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"session:flush\" is not defined.

Did you mean one of these?
    queue:flush
    session:table at C:\\xampp\\htdocs\\trash\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 C:\\xampp\\htdocs\\trash\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('session:flush')
#1 C:\\xampp\\htdocs\\trash\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 C:\\xampp\\htdocs\\trash\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\trash\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\trash\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#5 {main}
"} 
